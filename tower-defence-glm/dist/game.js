import { HexUtils } from './hex';
export class GameState {
    constructor() {
        this.points = 0;
        this.towers = [];
        this.unlockedRadius = 2;
        this.lastTime = performance.now();
        this.TOWER_COST = 50;
        this.POINTS_PER_TOWER_PER_SEC = 1;
        this.BASE_UNLOCKED_RADIUS = 2;
        this.POINTS_PER_UNLOCK_LEVEL = 20;
        console.log('GameState initialized');
        // Start with one free tower at (0,0)
        this.placeTower({ q: 0, r: 0 }, 0);
    }
    /**
     * Update game state with delta time
     */
    update(currentTime) {
        const deltaTime = (currentTime - this.lastTime) / 1000; // Convert to seconds
        this.lastTime = currentTime;
        // Generate points based on number of towers
        const pointsGenerated = this.towers.length * this.POINTS_PER_TOWER_PER_SEC * deltaTime;
        this.points += pointsGenerated;
        // Update unlocked radius based on points
        this.updateUnlockedRadius();
        console.log(`Game state updated - Points: ${this.points.toFixed(1)}, Towers: ${this.towers.length}, Radius: ${this.unlockedRadius}`);
    }
    /**
     * Place a tower at the specified coordinate
     */
    placeTower(coord, cost) {
        if (this.points < cost) {
            console.log(`Cannot place tower at (${coord.q}, ${coord.r}): insufficient points (${this.points.toFixed(1)} < ${cost})`);
            return false;
        }
        // Check if tower already exists at this coordinate
        if (this.towers.some(tower => tower.coord.q === coord.q && tower.coord.r === coord.r)) {
            console.log(`Cannot place tower at (${coord.q}, ${coord.r}): tower already exists`);
            return false;
        }
        // Deduct cost and place tower
        this.points -= cost;
        this.towers.push({
            coord,
            placedAt: performance.now()
        });
        console.log(`Tower placed at (${coord.q}, ${coord.r}) - Total towers: ${this.towers.length}`);
        return true;
    }
    /**
     * Try to place a new tower (costs 50 points)
     */
    tryPlaceNewTower(coord) {
        return this.placeTower(coord, this.TOWER_COST);
    }
    /**
     * Get current points
     */
    getPoints() {
        return this.points;
    }
    /**
     * Get number of towers
     */
    getTowerCount() {
        return this.towers.length;
    }
    /**
     * Get unlocked radius
     */
    getUnlockedRadius() {
        return this.unlockedRadius;
    }
    /**
     * Check if a hex coordinate is unlocked
     */
    isHexUnlocked(coord) {
        return HexUtils.distance({ q: 0, r: 0 }, coord) <= this.unlockedRadius;
    }
    /**
     * Check if a hex coordinate is empty (no tower)
     */
    isHexEmpty(coord) {
        return !this.towers.some(tower => tower.coord.q === coord.q && tower.coord.r === coord.r);
    }
    /**
     * Check if a hex coordinate is available for tower placement
     */
    canPlaceTower(coord) {
        return this.isHexUnlocked(coord) && this.isHexEmpty(coord);
    }
    /**
     * Update unlocked radius based on current points
     */
    updateUnlockedRadius() {
        const newRadius = this.BASE_UNLOCKED_RADIUS + Math.floor(this.points / this.POINTS_PER_UNLOCK_LEVEL);
        if (newRadius !== this.unlockedRadius) {
            console.log(`Unlocked radius increased from ${this.unlockedRadius} to ${newRadius}`);
            this.unlockedRadius = newRadius;
        }
    }
    /**
     * Get all hex coordinates that should be visible
     */
    getVisibleHexes() {
        const visibleHexes = [];
        const viewRadius = this.unlockedRadius + 2; // Add some buffer around unlocked area
        for (let q = -viewRadius; q <= viewRadius; q++) {
            for (let r = Math.max(-viewRadius, -q - viewRadius); r <= Math.min(viewRadius, -q + viewRadius); r++) {
                visibleHexes.push({ q, r });
            }
        }
        return visibleHexes;
    }
    /**
     * Get all hex coordinates that are unlocked
     */
    getUnlockedHexes() {
        return HexUtils.getHexesInRange({ q: 0, r: 0 }, this.unlockedRadius);
    }
    /**
     * Get towers array
     */
    getTowers() {
        return this.towers;
    }
    /**
     * Reset game state
     */
    reset() {
        this.points = 0;
        this.towers = [];
        this.unlockedRadius = this.BASE_UNLOCKED_RADIUS;
        this.lastTime = performance.now();
        this.placeTower({ q: 0, r: 0 }, 0);
        console.log('Game state reset');
    }
}
//# sourceMappingURL=game.js.map