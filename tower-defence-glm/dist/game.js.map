{"version": 3, "file": "game.js", "sourceRoot": "", "sources": ["../src/game.ts"], "names": [], "mappings": "AAAA,OAAO,EAAY,QAAQ,EAAE,MAAM,OAAO,CAAC;AAO3C,MAAM,OAAO,SAAS;IAUlB;QATQ,WAAM,GAAW,CAAC,CAAC;QACnB,WAAM,GAAY,EAAE,CAAC;QACrB,mBAAc,GAAW,CAAC,CAAC;QAC3B,aAAQ,GAAW,WAAW,CAAC,GAAG,EAAE,CAAC;QAC5B,eAAU,GAAW,EAAE,CAAC;QACxB,6BAAwB,GAAW,CAAC,CAAC;QACrC,yBAAoB,GAAW,CAAC,CAAC;QACjC,4BAAuB,GAAW,EAAE,CAAC;QAGlD,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QACrC,qCAAqC;QACrC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAmB;QACtB,MAAM,SAAS,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC,qBAAqB;QAC7E,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC;QAE5B,4CAA4C;QAC5C,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,wBAAwB,GAAG,SAAS,CAAC;QACvF,IAAI,CAAC,MAAM,IAAI,eAAe,CAAC;QAE/B,yCAAyC;QACzC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,OAAO,CAAC,GAAG,CAAC,gCAAgC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,IAAI,CAAC,MAAM,CAAC,MAAM,aAAa,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IACzI,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,KAAe,EAAE,IAAY;QACpC,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;YACrB,OAAO,CAAC,GAAG,CAAC,0BAA0B,KAAK,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,2BAA2B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,IAAI,GAAG,CAAC,CAAC;YACzH,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,mDAAmD;QACnD,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;YACpF,OAAO,CAAC,GAAG,CAAC,0BAA0B,KAAK,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,yBAAyB,CAAC,CAAC;YACpF,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,8BAA8B;QAC9B,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC;QACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACb,KAAK;YACL,QAAQ,EAAE,WAAW,CAAC,GAAG,EAAE;SAC9B,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,oBAAoB,KAAK,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,qBAAqB,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;QAC9F,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,KAAe;QAC5B,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,SAAS;QACL,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,aAAa;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,iBAAiB;QACb,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,KAAe;QACzB,OAAO,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC;IAC3E,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,KAAe;QACtB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC;IAC9F,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,KAAe;QACzB,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACK,oBAAoB;QACxB,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACrG,IAAI,SAAS,KAAK,IAAI,CAAC,cAAc,EAAE,CAAC;YACpC,OAAO,CAAC,GAAG,CAAC,kCAAkC,IAAI,CAAC,cAAc,OAAO,SAAS,EAAE,CAAC,CAAC;YACrF,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;QACpC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,eAAe;QACX,MAAM,YAAY,GAAe,EAAE,CAAC;QACpC,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,uCAAuC;QAEnF,KAAK,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,IAAI,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBACnG,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YAChC,CAAC;QACL,CAAC;QAED,OAAO,YAAY,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,gBAAgB;QACZ,OAAO,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;IACzE,CAAC;IAED;;OAEG;IACH,SAAS;QACL,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK;QACD,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAChD,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAClC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACnC,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IACpC,CAAC;CACJ"}