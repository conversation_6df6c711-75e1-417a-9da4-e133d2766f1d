export class HexUtils {
    /**
     * Convert axial coordinates to pixel coordinates
     */
    static axialToPixel(coord) {
        const x = HexUtils.HEX_SIZE * (Math.sqrt(3) * coord.q + Math.sqrt(3) / 2 * coord.r);
        const y = HexUtils.HEX_SIZE * (3 / 2 * coord.r);
        return { x, y };
    }
    /**
     * Convert pixel coordinates to axial coordinates
     */
    static pixelToAxial(x, y) {
        const q = (Math.sqrt(3) / 3 * x - 1 / 3 * y) / HexUtils.HEX_SIZE;
        const r = (2 / 3 * y) / HexUtils.HEX_SIZE;
        return this.roundAxial(q, r);
    }
    /**
     * Round fractional axial coordinates to nearest hex
     */
    static roundAxial(q, r) {
        const s = -q - r;
        let rq = Math.round(q);
        let rr = Math.round(r);
        let rs = Math.round(s);
        const q_diff = Math.abs(rq - q);
        const r_diff = Math.abs(rr - r);
        const s_diff = Math.abs(rs - s);
        if (q_diff > r_diff && q_diff > s_diff) {
            rq = -rr - rs;
        }
        else if (r_diff > s_diff) {
            rr = -rq - rs;
        }
        return { q: rq, r: rr };
    }
    /**
     * Calculate distance between two hex coordinates
     */
    static distance(a, b) {
        return (Math.abs(a.q - b.q) + Math.abs(a.q + a.r - b.q - b.r) + Math.abs(a.r - b.r)) / 2;
    }
    /**
     * Get all hex coordinates within a given radius from center
     */
    static getHexesInRange(center, radius) {
        const hexes = [];
        for (let q = -radius; q <= radius; q++) {
            for (let r = Math.max(-radius, -q - radius); r <= Math.min(radius, -q + radius); r++) {
                hexes.push({ q: center.q + q, r: center.r + r });
            }
        }
        return hexes;
    }
    /**
     * Get hex vertices for drawing
     */
    static getHexVertices(coord) {
        const center = this.axialToPixel(coord);
        const vertices = [];
        for (let i = 0; i < 6; i++) {
            const angle = (Math.PI / 3) * i;
            const x = center.x + HexUtils.HEX_SIZE * Math.cos(angle);
            const y = center.y + HexUtils.HEX_SIZE * Math.sin(angle);
            vertices.push({ x, y });
        }
        return vertices;
    }
    /**
     * Get hex bounding box for collision detection
     */
    static getHexBounds(coord) {
        const center = this.axialToPixel(coord);
        const halfWidth = HexUtils.HEX_WIDTH / 2;
        const halfHeight = HexUtils.HEX_HEIGHT / 2;
        return {
            minX: center.x - halfWidth,
            minY: center.y - halfHeight,
            maxX: center.x + halfWidth,
            maxY: center.y + halfHeight
        };
    }
    /**
     * Check if a point is inside a hex
     */
    static isPointInHex(point, coord) {
        const bounds = this.getHexBounds(coord);
        return point.x >= bounds.minX && point.x <= bounds.maxX &&
            point.y >= bounds.minY && point.y <= bounds.maxY;
    }
    /**
     * Get hex size
     */
    static getHexSize() {
        return HexUtils.HEX_SIZE;
    }
}
HexUtils.HEX_SIZE = 30;
HexUtils.HEX_WIDTH = Math.sqrt(3) * HexUtils.HEX_SIZE;
HexUtils.HEX_HEIGHT = 2 * HexUtils.HEX_SIZE;
//# sourceMappingURL=hex.js.map