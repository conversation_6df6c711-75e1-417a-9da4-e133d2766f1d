export class HUD {
    constructor() {
        // Get HUD elements from DOM
        this.pointsElement = document.getElementById('points');
        this.towersElement = document.getElementById('towers');
        this.radiusElement = document.getElementById('radius');
        if (!this.pointsElement || !this.towersElement || !this.radiusElement) {
            throw new Error('HUD elements not found in DOM');
        }
        console.log('HUD initialized');
    }
    /**
     * Update HUD with current game state
     */
    update(points, towerCount, radius) {
        // Update points with 1 decimal place
        this.pointsElement.textContent = points.toFixed(1);
        // Update tower count
        this.towersElement.textContent = towerCount.toString();
        // Update unlocked radius
        this.radiusElement.textContent = radius.toString();
        console.log(`HUD updated - Points: ${points.toFixed(1)}, Towers: ${towerCount}, Radius: ${radius}`);
    }
    /**
     * Show error message temporarily
     */
    showError(message, duration = 2000) {
        const originalPointsText = this.pointsElement.textContent;
        this.pointsElement.textContent = message;
        this.pointsElement.style.color = '#ff4444';
        setTimeout(() => {
            this.pointsElement.textContent = originalPointsText;
            this.pointsElement.style.color = '';
        }, duration);
        console.log(`HUD error: ${message}`);
    }
    /**
     * Show success message temporarily
     */
    showSuccess(message, duration = 2000) {
        const originalPointsText = this.pointsElement.textContent;
        this.pointsElement.textContent = message;
        this.pointsElement.style.color = '#44ff44';
        setTimeout(() => {
            this.pointsElement.textContent = originalPointsText;
            this.pointsElement.style.color = '';
        }, duration);
        console.log(`HUD success: ${message}`);
    }
    /**
     * Highlight tower count when it changes
     */
    highlightTowerCount() {
        this.towersElement.style.color = '#44ff44';
        setTimeout(() => {
            this.towersElement.style.color = '';
        }, 500);
        console.log('Tower count highlighted');
    }
    /**
     * Highlight radius when it increases
     */
    highlightRadius() {
        this.radiusElement.style.color = '#ffff44';
        setTimeout(() => {
            this.radiusElement.style.color = '';
        }, 1000);
        console.log('Radius highlighted');
    }
}
//# sourceMappingURL=hud.js.map