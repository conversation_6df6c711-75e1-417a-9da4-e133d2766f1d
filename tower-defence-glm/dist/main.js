import { GameState } from './game';
import { Renderer } from './renderer';
import { HUD } from './hud';
export class Game {
    constructor() {
        this.isRunning = false;
        this.TOWER_COST = 50;
        // Initialize canvas
        this.canvas = document.getElementById('gameCanvas');
        if (!this.canvas) {
            throw new Error('Canvas element not found');
        }
        // Set canvas size
        this.resizeCanvas();
        window.addEventListener('resize', () => this.resizeCanvas());
        // Initialize game components
        this.gameState = new GameState();
        this.renderer = new Renderer(this.canvas, this.gameState);
        this.hud = new HUD();
        // Set up camera
        this.renderer.setCamera(0, 0, 1);
        // Set up event listeners
        this.setupEventListeners();
        console.log('Game initialized');
    }
    /**
     * Resize canvas to fill window
     */
    resizeCanvas() {
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;
        this.renderer.setCanvasSize(this.canvas.width, this.canvas.height);
        console.log(`Canvas resized to ${this.canvas.width}x${this.canvas.height}`);
    }
    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Handle canvas clicks
        this.canvas.addEventListener('click', (event) => {
            this.handleCanvasClick(event);
        });
        // Handle mouse wheel for zooming
        this.canvas.addEventListener('wheel', (event) => {
            this.handleWheel(event);
        });
        // Handle mouse move for hover effects
        this.canvas.addEventListener('mousemove', (event) => {
            this.handleMouseMove(event);
        });
        console.log('Event listeners set up');
    }
    /**
     * Handle canvas click events
     */
    handleCanvasClick(event) {
        const rect = this.canvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        const hexCoord = this.renderer.getHexFromScreen(x, y);
        if (hexCoord) {
            console.log(`Clicked on hex (${hexCoord.q}, ${hexCoord.r})`);
            if (this.gameState.canPlaceTower(hexCoord)) {
                if (this.gameState.tryPlaceNewTower(hexCoord)) {
                    this.hud.showSuccess(`Tower placed! -${this.TOWER_COST} points`);
                    this.hud.highlightTowerCount();
                    console.log(`Tower successfully placed at (${hexCoord.q}, ${hexCoord.r})`);
                }
                else {
                    this.hud.showError('Not enough points!');
                    console.log(`Failed to place tower at (${hexCoord.q}, ${hexCoord.r}): insufficient points`);
                }
            }
            else {
                if (!this.gameState.isHexUnlocked(hexCoord)) {
                    this.hud.showError('Hex locked!');
                    console.log(`Cannot place tower at (${hexCoord.q}, ${hexCoord.r}): hex is locked`);
                }
                else {
                    this.hud.showError('Tower already exists!');
                    console.log(`Cannot place tower at (${hexCoord.q}, ${hexCoord.r}): tower already exists`);
                }
            }
        }
        else {
            console.log('Clicked outside hex grid');
        }
    }
    /**
     * Handle mouse wheel events for zooming
     */
    handleWheel(event) {
        event.preventDefault();
        const zoomSpeed = 0.1;
        const currentZoom = this.renderer['camera'].zoom;
        const newZoom = Math.max(0.1, Math.min(3, currentZoom + (event.deltaY > 0 ? -zoomSpeed : zoomSpeed)));
        this.renderer.setCamera(this.renderer['camera'].x, this.renderer['camera'].y, newZoom);
        console.log(`Zoom changed to ${newZoom}`);
    }
    /**
     * Handle mouse move events
     */
    handleMouseMove(event) {
        const rect = this.canvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        const hexCoord = this.renderer.getHexFromScreen(x, y);
        // Change cursor based on hover state
        if (hexCoord && this.gameState.canPlaceTower(hexCoord)) {
            this.canvas.style.cursor = 'pointer';
        }
        else {
            this.canvas.style.cursor = 'crosshair';
        }
    }
    /**
     * Start the game loop
     */
    start() {
        if (this.isRunning) {
            console.warn('Game is already running');
            return;
        }
        this.isRunning = true;
        this.gameLoop();
        console.log('Game started');
    }
    /**
     * Stop the game loop
     */
    stop() {
        this.isRunning = false;
        console.log('Game stopped');
    }
    /**
     * Main game loop
     */
    gameLoop() {
        if (!this.isRunning)
            return;
        const currentTime = performance.now();
        // Update game state
        this.gameState.update(currentTime);
        // Update HUD
        this.hud.update(this.gameState.getPoints(), this.gameState.getTowerCount(), this.gameState.getUnlockedRadius());
        // Check for radius increase
        const previousRadius = 0; // Store previous radius in game state if needed
        if (this.gameState.getUnlockedRadius() > previousRadius) {
            this.hud.highlightRadius();
        }
        // Render
        this.renderer.render();
        // Schedule next frame
        requestAnimationFrame(() => this.gameLoop());
    }
    /**
     * Reset the game
     */
    reset() {
        this.gameState.reset();
        this.hud.update(this.gameState.getPoints(), this.gameState.getTowerCount(), this.gameState.getUnlockedRadius());
        console.log('Game reset');
    }
}
// Initialize game when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    try {
        const game = new Game();
        game.start();
        console.log('Game successfully started');
    }
    catch (error) {
        console.error('Failed to initialize game:', error);
    }
});
//# sourceMappingURL=main.js.map