import { HexUtils } from './hex';
export class Renderer {
    constructor(canvas, gameState) {
        this.TOWER_COLOR = '#4CAF50';
        this.UNLOCKED_HEX_COLOR = '#666666';
        this.LOCKED_HEX_COLOR = '#333333';
        this.UNLOCKED_HEX_STROKE = '#888888';
        this.LOCKED_HEX_STROKE = '#555555';
        this.TOWER_STROKE = '#2E7D32';
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.gameState = gameState;
        this.camera = { x: 0, y: 0, zoom: 1 };
        console.log('Renderer initialized with canvas:', canvas.width, 'x', canvas.height);
    }
    /**
     * Set canvas size
     */
    setCanvasSize(width, height) {
        this.canvas.width = width;
        this.canvas.height = height;
        console.log(`Canvas size set to ${width}x${height}`);
    }
    /**
     * Update camera position
     */
    setCamera(x, y, zoom) {
        this.camera = { x, y, zoom };
        console.log(`Camera updated to position (${x}, ${y}) with zoom ${zoom}`);
    }
    /**
     * Render the game
     */
    render() {
        const startTime = performance.now();
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        // Save context state
        this.ctx.save();
        // Apply camera transform
        this.ctx.translate(this.canvas.width / 2, this.canvas.height / 2);
        this.ctx.scale(this.camera.zoom, this.camera.zoom);
        this.ctx.translate(this.camera.x, this.camera.y);
        // Get visible hexes
        const visibleHexes = this.gameState.getVisibleHexes();
        const unlockedHexes = this.gameState.getUnlockedHexes();
        console.log(`Rendering ${visibleHexes.length} visible hexes, ${unlockedHexes.length} unlocked hexes`);
        // Render hexes
        visibleHexes.forEach(hex => {
            this.renderHex(hex, unlockedHexes);
        });
        // Render towers
        this.gameState.getTowerCount() > 0 && this.gameState.getTowerCount() <= 100 &&
            this.gameState.getTowerCount() % 10 === 0 && console.log(`Rendering ${this.gameState.getTowerCount()} towers`);
        this.gameState.getTowerCount() > 0 && this.gameState.getTowerCount() <= 100 &&
            this.gameState.getTowerCount() % 10 === 0 && this.gameState.getTowers().forEach((tower) => {
            this.renderTower(tower.coord);
        });
        // Restore context state
        this.ctx.restore();
        const renderTime = performance.now() - startTime;
        if (renderTime > 16) { // Log if rendering takes more than 16ms
            console.warn(`Render time: ${renderTime.toFixed(2)}ms`);
        }
    }
    /**
     * Render a single hex
     */
    renderHex(coord, unlockedHexes) {
        const vertices = HexUtils.getHexVertices(coord);
        const isUnlocked = unlockedHexes.some(unlocked => unlocked.q === coord.q && unlocked.r === coord.r);
        // Set fill and stroke colors based on whether hex is unlocked
        this.ctx.fillStyle = isUnlocked ? this.UNLOCKED_HEX_COLOR : this.LOCKED_HEX_COLOR;
        this.ctx.strokeStyle = isUnlocked ? this.UNLOCKED_HEX_STROKE : this.LOCKED_HEX_STROKE;
        this.ctx.lineWidth = 1 / this.camera.zoom;
        // Draw hex
        this.ctx.beginPath();
        this.ctx.moveTo(vertices[0].x, vertices[0].y);
        for (let i = 1; i < vertices.length; i++) {
            this.ctx.lineTo(vertices[i].x, vertices[i].y);
        }
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.stroke();
        // Draw coordinate text for debugging (optional)
        if (this.camera.zoom > 0.5) {
            const center = HexUtils.axialToPixel(coord);
            this.ctx.fillStyle = isUnlocked ? '#999999' : '#666666';
            this.ctx.font = `${10 / this.camera.zoom}px Arial`;
            this.ctx.textAlign = 'center';
            this.ctx.textBaseline = 'middle';
            this.ctx.fillText(`${coord.q},${coord.r}`, center.x, center.y);
        }
    }
    /**
     * Render a tower
     */
    renderTower(coord) {
        const center = HexUtils.axialToPixel(coord);
        const hexSize = HexUtils.getHexSize();
        // Draw tower as a circle
        this.ctx.fillStyle = this.TOWER_COLOR;
        this.ctx.strokeStyle = this.TOWER_STROKE;
        this.ctx.lineWidth = 2 / this.camera.zoom;
        this.ctx.beginPath();
        this.ctx.arc(center.x, center.y, hexSize * 0.4 * this.camera.zoom, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();
        // Draw tower icon (simple square)
        this.ctx.fillStyle = '#2E7D32';
        const towerSize = hexSize * 0.2 * this.camera.zoom;
        this.ctx.fillRect(center.x - towerSize / 2, center.y - towerSize / 2, towerSize, towerSize);
    }
    /**
     * Convert screen coordinates to world coordinates
     */
    screenToWorld(screenX, screenY) {
        return {
            x: (screenX - this.canvas.width / 2) / this.camera.zoom - this.camera.x,
            y: (screenY - this.canvas.height / 2) / this.camera.zoom - this.camera.y
        };
    }
    /**
     * Get hex coordinate from screen coordinates
     */
    getHexFromScreen(screenX, screenY) {
        const worldPos = this.screenToWorld(screenX, screenY);
        const hexCoord = HexUtils.pixelToAxial(worldPos.x, worldPos.y);
        // Check if the hex is actually under the mouse (not just the bounding box)
        if (HexUtils.isPointInHex(worldPos, hexCoord)) {
            return hexCoord;
        }
        return null;
    }
}
//# sourceMappingURL=renderer.js.map