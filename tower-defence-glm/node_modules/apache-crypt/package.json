{"name": "apache-crypt", "description": "Node.js module for Apache style password encryption using crypt(3).", "version": "1.2.6", "author": "<PERSON><PERSON><PERSON> (http://github.com/gevorg)", "maintainers": [{"name": "g<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "http://github.com/gevorg/apache-crypt", "repository": {"type": "git", "url": "http://github.com/gevorg/apache-crypt.git"}, "main": "./src/index.js", "licenses": [{"type": "MIT", "url": "http://github.com/gevorg/apache-crypt/blob/master/LICENSE"}], "license": "MIT", "bugs": {"url": "http://github.com/gevorg/apache-crypt/issues"}, "dependencies": {"unix-crypt-td-js": "^1.1.4"}, "devDependencies": {"chai": "^4.2.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-node": "^11.0.0", "eslint-plugin-prettier": "^3.1.2", "mocha": "^7.0.1", "prettier": "^1.19.1"}, "engines": {"node": ">=8"}, "scripts": {"test": "mocha", "pretest": "eslint --ignore-path .gitignore ."}, "keywords": ["apache", "crypt", "password", "htpasswd"]}