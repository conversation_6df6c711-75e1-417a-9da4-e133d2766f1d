{"name": "connect", "description": "High performance middleware framework", "version": "3.7.0", "author": "<PERSON><PERSON> <<EMAIL>> (http://tjholowaychuk.com)", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "keywords": ["framework", "web", "middleware", "connect", "rack"], "repository": "senchalabs/connect", "dependencies": {"debug": "2.6.9", "finalhandler": "1.1.2", "parseurl": "~1.3.3", "utils-merge": "1.0.1"}, "devDependencies": {"eslint": "4.19.1", "mocha": "6.1.4", "nyc": "14.1.1", "supertest": "4.0.2"}, "license": "MIT", "files": ["LICENSE", "HISTORY.md", "README.md", "SECURITY.md", "index.js"], "engines": {"node": ">= 0.10.0"}, "scripts": {"lint": "eslint .", "test": "mocha --require test/support/env --reporter spec --bail --check-leaks test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}}